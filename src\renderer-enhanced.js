class UnrestrictedBrowser {
    constructor() {
        console.log('UnrestrictedBrowser constructor called');
        this.websites = [];
        this.tabs = [];
        this.activeTabId = null;
        this.history = [];
        this.downloads = [];
        this.zoomLevel = 1.0;
        this.settings = {
            autoLoadLastWebsite: false,
            showNotifications: true,
            clearDataOnExit: false,
            blockAds: false,
            homepage: 'data:text/html,<html><body style="margin:0;padding:20px;font-family:Arial,sans-serif;background:#f5f5f5;"><h1>Home</h1><p>Welcome to your browser homepage.</p></body></html>'
        };

        console.log('Calling init()...');
        this.init();
    }

    async init() {
        console.log('init() called');
        try {
            console.log('Loading data...');
            await this.loadData();
            console.log('Data loaded successfully');

            console.log('Setting up event listeners...');
            this.setupEventListeners();
            console.log('Event listeners set up');

            console.log('Setting up Electron listeners...');
            this.setupElectronListeners();
            console.log('Electron listeners set up');

            console.log('Rendering website list...');
            this.renderWebsiteList();
            console.log('Website list rendered');

            console.log('Updating stats...');
            this.updateStats();
            console.log('Stats updated');

            // Create initial tab
            console.log('Creating initial tab...');
            this.createNewTab();
            console.log('Initial tab created');

            if (this.websites.length === 0) {
                console.log('No websites found, showing welcome screen');
                this.showWelcomeScreen();
            } else if (this.settings.autoLoadLastWebsite) {
                console.log('Loading last website...');
                const lastWebsite = await window.electronAPI.storeGet('lastWebsite');
                if (lastWebsite) {
                    const website = this.websites.find(w => w.id === lastWebsite);
                    if (website) {
                        this.loadWebsite(website);
                    }
                }
            }
            console.log('init() completed successfully');
        } catch (error) {
            console.error('Error in init():', error);
        }
    }

    async loadData() {
        try {
            console.log('Loading stored data...');
            const websites = await window.electronAPI.storeGet('websites');
            const settings = await window.electronAPI.storeGet('settings');
            const history = await window.electronAPI.storeGet('history');
            const downloads = await window.electronAPI.storeGet('downloads');

            console.log('Raw websites data:', websites);

            // Filter out any problematic websites
            const filteredWebsites = (websites || []).filter(website => {
                if (!website.url) return false;
                // Filter out inappropriate or problematic URLs
                const url = website.url.toLowerCase();
                const blockedDomains = ['pornhub', 'hentai', 'xxx', 'porn', 'adult'];
                return !blockedDomains.some(domain => url.includes(domain));
            });

            console.log('Filtered websites:', filteredWebsites);

            this.websites = filteredWebsites;
            this.settings = { ...this.settings, ...(settings || {}) };
            this.history = history || [];
            this.downloads = downloads || [];

            // If we filtered out websites, save the cleaned data
            if (websites && websites.length !== filteredWebsites.length) {
                console.log('Cleaned up problematic websites, saving...');
                await this.saveData();
            }
        } catch (error) {
            console.error('Error loading data:', error);
        }
    }

    async saveData() {
        try {
            await window.electronAPI.storeSet('websites', this.websites);
            await window.electronAPI.storeSet('settings', this.settings);
            await window.electronAPI.storeSet('history', this.history);
            await window.electronAPI.storeSet('downloads', this.downloads);
        } catch (error) {
            console.error('Error saving data:', error);
        }
    }

    // Tab Management
    createNewTab(url = null) {
        console.log('createNewTab called with URL:', url);
        const tabId = this.generateId();
        const tab = {
            id: tabId,
            title: 'New Tab',
            url: url || 'about:blank',
            favicon: null,
            loading: false,
            canGoBack: false,
            canGoForward: false,
            webview: null
        };
        console.log('Created tab:', tab);

        this.tabs.push(tab);
        this.renderTabs();
        this.switchToTab(tabId);
        
        if (url) {
            this.navigateToUrl(url);
        }

        return tab;
    }

    closeTab(tabId) {
        const tabIndex = this.tabs.findIndex(t => t.id === tabId);
        if (tabIndex === -1) return;

        const tab = this.tabs[tabIndex];
        
        // Remove webview
        if (tab.webview && tab.webview.parentNode) {
            tab.webview.parentNode.removeChild(tab.webview);
        }

        this.tabs.splice(tabIndex, 1);

        // If this was the active tab, switch to another
        if (this.activeTabId === tabId) {
            if (this.tabs.length > 0) {
                const newActiveIndex = Math.min(tabIndex, this.tabs.length - 1);
                this.switchToTab(this.tabs[newActiveIndex].id);
            } else {
                this.activeTabId = null;
                this.createNewTab();
            }
        }

        this.renderTabs();
    }

    switchToTab(tabId) {
        const tab = this.tabs.find(t => t.id === tabId);
        if (!tab) return;

        // Hide all webviews
        this.tabs.forEach(t => {
            if (t.webview) {
                t.webview.style.display = 'none';
            }
        });

        this.activeTabId = tabId;
        
        // Hide welcome screen
        this.hideWelcomeScreen();
        
        // Show active webview or create it
        if (!tab.webview) {
            this.createWebviewForTab(tab);
        } else {
            tab.webview.style.display = 'block';
            // Only update navigation buttons after dom-ready event
            tab.webview.addEventListener('dom-ready', () => {
                this.updateNavigationButtons();
            }, { once: true });
        }

        // Update UI
        this.updateUrlBar(tab.url);
        this.renderTabs();
        this.updateZoomDisplay();
    }

    duplicateTab(tabId) {
        const tab = this.tabs.find(t => t.id === tabId);
        if (tab) {
            this.createNewTab(tab.url);
        }
    }

    renderTabs() {
        const container = document.getElementById('tabsContainer');
        container.innerHTML = '';

        this.tabs.forEach(tab => {
            const tabElement = this.createTabElement(tab);
            container.appendChild(tabElement);
        });
    }

    createTabElement(tab) {
        const tabEl = document.createElement('div');
        tabEl.className = `tab ${tab.id === this.activeTabId ? 'active' : ''} ${tab.loading ? 'loading' : ''}`;
        tabEl.setAttribute('data-tab-id', tab.id);

        const favicon = document.createElement('div');
        favicon.className = 'tab-favicon';
        if (tab.favicon) {
            favicon.innerHTML = `<img src="${tab.favicon}" width="16" height="16">`;
        } else {
            favicon.innerHTML = '<i class="fas fa-globe"></i>';
        }

        const title = document.createElement('div');
        title.className = 'tab-title';
        title.textContent = tab.title || 'New Tab';
        title.title = tab.title || 'New Tab';

        const closeBtn = document.createElement('button');
        closeBtn.className = 'tab-close';
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        closeBtn.title = 'Close tab';

        tabEl.appendChild(favicon);
        tabEl.appendChild(title);
        tabEl.appendChild(closeBtn);

        // Event listeners
        tabEl.addEventListener('click', (e) => {
            if (e.target === closeBtn || e.target.parentNode === closeBtn) {
                e.stopPropagation();
                this.closeTab(tab.id);
            } else {
                this.switchToTab(tab.id);
            }
        });

        tabEl.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showTabContextMenu(e, tab);
        });

        return tabEl;
    }

    createWebviewForTab(tab) {
        console.log('createWebviewForTab called for tab:', tab.id, 'URL:', tab.url);
        const container = document.getElementById('webviewContainer');
        console.log('Webview container found:', !!container);

        if (!container) {
            console.error('Webview container not found!');
            return;
        }

        const webview = document.createElement('webview');
        console.log('Created webview element:', webview);
        webview.src = tab.url;
        webview.style.cssText = `
            width: 100% !important;
            height: 100% !important;
            display: block !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            border: none !important;
            outline: none !important;
            background: #ffffff !important;
            max-width: none !important;
            min-width: 0 !important;
        `;
        webview.setAttribute('data-tab-id', tab.id);
        console.log('Webview configured with src:', webview.src);
        
        // Webview event listeners
        webview.addEventListener('dom-ready', () => {
            this.updateTabLoading(tab.id, false);
            tab.domReady = true;
            this.updateNavigationButtons();
        });

        webview.addEventListener('did-start-loading', () => {
            this.updateTabLoading(tab.id, true);
            this.showLoadingIndicator();
        });

        webview.addEventListener('did-stop-loading', () => {
            this.updateTabLoading(tab.id, false);
            this.hideLoadingIndicator();
            this.updateNavigationButtons();
        });

        webview.addEventListener('did-navigate', (e) => {
            this.updateTabUrl(tab.id, e.url);
            this.addToHistory(e.url, tab.title);
            this.updateUrlBar(e.url);
            this.updateNavigationButtons();
            this.updateSecurityIndicator(e.url);
        });

        webview.addEventListener('did-navigate-in-page', (e) => {
            this.updateTabUrl(tab.id, e.url);
            this.updateUrlBar(e.url);
        });

        webview.addEventListener('page-title-updated', (e) => {
            this.updateTabTitle(tab.id, e.title);
        });

        webview.addEventListener('page-favicon-updated', (e) => {
            if (e.favicons && e.favicons.length > 0) {
                this.updateTabFavicon(tab.id, e.favicons[0]);
            }
        });

        webview.addEventListener('did-fail-load', (e) => {
            console.error('Webview failed to load:', e.errorDescription, 'for URL:', e.validatedURL);
            this.updateTabLoading(tab.id, false);
        });

        // Download handling
        webview.addEventListener('will-download', (e) => {
            this.handleDownload(e);
        });

        console.log('Appending webview to container...');
        container.appendChild(webview);
        tab.webview = webview;

        console.log('Webview appended successfully');
        console.log('Container children count:', container.children.length);
        console.log('Webview parent:', webview.parentNode);
        console.log('Webview display style:', webview.style.display);
    }

    updateTabLoading(tabId, loading) {
        const tab = this.tabs.find(t => t.id === tabId);
        if (tab) {
            tab.loading = loading;
            this.renderTabs();
        }
    }

    updateTabTitle(tabId, title) {
        const tab = this.tabs.find(t => t.id === tabId);
        if (tab) {
            tab.title = title;
            this.renderTabs();
        }
    }

    updateTabUrl(tabId, url) {
        const tab = this.tabs.find(t => t.id === tabId);
        if (tab) {
            tab.url = url;
        }
    }

    updateTabFavicon(tabId, favicon) {
        const tab = this.tabs.find(t => t.id === tabId);
        if (tab) {
            tab.favicon = favicon;
            this.renderTabs();
        }
    }

    // History Management
    addToHistory(url, title) {
        if (url === 'about:blank' || url.startsWith('chrome://')) return;

        const historyItem = {
            id: this.generateId(),
            url,
            title: title || url,
            timestamp: new Date().toISOString(),
            favicon: null
        };

        // Remove duplicate entries
        this.history = this.history.filter(item => item.url !== url);
        this.history.unshift(historyItem);

        // Limit history to 1000 items
        if (this.history.length > 1000) {
            this.history = this.history.slice(0, 1000);
        }

        this.saveData();
    }

    showHistoryModal() {
        const modal = document.getElementById('historyModal');
        modal.classList.add('show');
        this.renderHistory();
    }

    hideHistoryModal() {
        const modal = document.getElementById('historyModal');
        modal.classList.remove('show');
    }

    renderHistory() {
        const container = document.getElementById('historyList');
        container.innerHTML = '';

        if (this.history.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-history"></i>
                    <p>No browsing history</p>
                </div>
            `;
            return;
        }

        this.history.forEach(item => {
            const historyEl = this.createHistoryItem(item);
            container.appendChild(historyEl);
        });
    }

    createHistoryItem(item) {
        const itemEl = document.createElement('div');
        itemEl.className = 'history-item';

        const favicon = document.createElement('div');
        favicon.className = 'history-favicon';
        favicon.innerHTML = '<i class="fas fa-globe"></i>';

        const info = document.createElement('div');
        info.className = 'history-info';

        const title = document.createElement('div');
        title.className = 'history-title';
        title.textContent = item.title;

        const url = document.createElement('div');
        url.className = 'history-url';
        url.textContent = item.url;

        const time = document.createElement('div');
        time.className = 'history-time';
        time.textContent = this.formatTime(item.timestamp);

        info.appendChild(title);
        info.appendChild(url);

        itemEl.appendChild(favicon);
        itemEl.appendChild(info);
        itemEl.appendChild(time);

        itemEl.addEventListener('click', () => {
            this.navigateToUrl(item.url);
            this.hideHistoryModal();
        });

        return itemEl;
    }

    clearHistory() {
        this.history = [];
        this.saveData();
        this.renderHistory();
        this.showNotification('History cleared');
    }

    // Download Management
    handleDownload(event) {
        const download = {
            id: this.generateId(),
            filename: event.filename || 'download',
            url: event.url,
            state: 'in_progress',
            progress: 0,
            totalBytes: event.totalBytes || 0,
            receivedBytes: 0,
            startTime: new Date().toISOString()
        };

        this.downloads.unshift(download);
        this.saveData();
        this.renderDownloads();
        
        if (this.settings.showNotifications) {
            this.showNotification(`Download started: ${download.filename}`);
        }
    }

    showDownloadsModal() {
        const modal = document.getElementById('downloadsModal');
        modal.classList.add('show');
        this.renderDownloads();
    }

    hideDownloadsModal() {
        const modal = document.getElementById('downloadsModal');
        modal.classList.remove('show');
    }

    renderDownloads() {
        const container = document.getElementById('downloadsList');
        
        if (this.downloads.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-download"></i>
                    <p>No downloads yet</p>
                </div>
            `;
            return;
        }

        container.innerHTML = '';
        this.downloads.forEach(download => {
            const downloadEl = this.createDownloadItem(download);
            container.appendChild(downloadEl);
        });
    }

    createDownloadItem(download) {
        const itemEl = document.createElement('div');
        itemEl.className = 'download-item';

        const icon = document.createElement('div');
        icon.className = 'download-icon';
        icon.innerHTML = '<i class="fas fa-file"></i>';

        const info = document.createElement('div');
        info.className = 'download-info';

        const name = document.createElement('div');
        name.className = 'download-name';
        name.textContent = download.filename;

        const details = document.createElement('div');
        details.className = 'download-details';
        details.innerHTML = `
            <span>${this.formatFileSize(download.totalBytes)}</span>
            <span>${download.state}</span>
            <span>${this.formatTime(download.startTime)}</span>
        `;

        info.appendChild(name);
        info.appendChild(details);

        if (download.state === 'in_progress') {
            const progress = document.createElement('div');
            progress.className = 'download-progress';
            const progressBar = document.createElement('div');
            progressBar.className = 'download-progress-bar';
            progressBar.style.width = `${download.progress}%`;
            progress.appendChild(progressBar);
            info.appendChild(progress);
        }

        const actions = document.createElement('div');
        actions.className = 'download-actions';
        
        if (download.state === 'completed') {
            const openBtn = document.createElement('button');
            openBtn.className = 'btn-icon';
            openBtn.innerHTML = '<i class="fas fa-external-link-alt"></i>';
            openBtn.title = 'Open file';
            actions.appendChild(openBtn);
        }

        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'btn-icon';
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        deleteBtn.title = 'Remove from list';
        deleteBtn.addEventListener('click', () => {
            this.removeDownload(download.id);
        });
        actions.appendChild(deleteBtn);

        itemEl.appendChild(icon);
        itemEl.appendChild(info);
        itemEl.appendChild(actions);

        return itemEl;
    }

    removeDownload(downloadId) {
        this.downloads = this.downloads.filter(d => d.id !== downloadId);
        this.saveData();
        this.renderDownloads();
    }

    clearDownloads() {
        this.downloads = [];
        this.saveData();
        this.renderDownloads();
        this.showNotification('Downloads cleared');
    }

    // Find in Page
    showFindBar() {
        const findBar = document.getElementById('findBar');
        const findInput = document.getElementById('findInput');
        findBar.classList.add('show');
        findInput.focus();
    }

    hideFindBar() {
        const findBar = document.getElementById('findBar');
        findBar.classList.remove('show');
        this.clearFind();
    }

    findInPage(text, forward = true) {
        const activeTab = this.tabs.find(t => t.id === this.activeTabId);
        if (!activeTab || !activeTab.webview) return;

        activeTab.webview.findInPage(text, {
            forward,
            findNext: true
        });
    }

    clearFind() {
        const activeTab = this.tabs.find(t => t.id === this.activeTabId);
        if (activeTab && activeTab.webview) {
            activeTab.webview.stopFindInPage('clearSelection');
        }
    }

    // Navigation and URL handling
    navigateToUrl(url) {
        if (!url) return;

        console.log('Navigating to URL:', url);

        // Add protocol if missing
        if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('about:') && !url.startsWith('data:')) {
            // Check if it looks like a search query
            if (!url.includes('.') || url.includes(' ')) {
                url = `https://www.google.com/search?q=${encodeURIComponent(url)}`;
            } else {
                url = 'https://' + url;
            }
        }

        const activeTab = this.tabs.find(t => t.id === this.activeTabId);
        console.log('Active tab:', activeTab);
        
        if (activeTab) {
            if (activeTab.webview) {
                console.log('Setting webview src to:', url);
                activeTab.webview.src = url;
            } else {
                console.log('Creating webview for tab');
                activeTab.url = url;
                this.createWebviewForTab(activeTab);
            }
            this.updateUrlBar(url);
        } else {
            console.log('No active tab found');
        }
    }

    updateUrlBar(url) {
        const urlInput = document.getElementById('urlInput');
        if (urlInput) {
            urlInput.value = url;
        }
    }

    updateSecurityIndicator(url) {
        const indicator = document.getElementById('securityIndicator');
        if (url.startsWith('https://')) {
            indicator.className = 'security-indicator secure';
            indicator.innerHTML = '<i class="fas fa-lock" title="Secure (HTTPS)"></i>';
        } else if (url.startsWith('http://')) {
            indicator.className = 'security-indicator insecure';
            indicator.innerHTML = '<i class="fas fa-unlock" title="Not secure (HTTP)"></i>';
        } else {
            indicator.className = 'security-indicator';
            indicator.innerHTML = '<i class="fas fa-info-circle" title="Local page"></i>';
        }
    }

    updateNavigationButtons() {
        const activeTab = this.tabs.find(t => t.id === this.activeTabId);
        const backBtn = document.getElementById('backBtn');
        const forwardBtn = document.getElementById('forwardBtn');

        if (backBtn && forwardBtn && activeTab && activeTab.webview) {
            // Only call canGoBack and canGoForward if webview is attached and dom-ready event emitted
            if (activeTab.webview.isConnected && activeTab.domReady) {
                try {
                    backBtn.disabled = !activeTab.webview.canGoBack();
                    forwardBtn.disabled = !activeTab.webview.canGoForward();
                } catch (e) {
                    // If error occurs, disable buttons as fallback
                    backBtn.disabled = true;
                    forwardBtn.disabled = true;
                }
            } else {
                backBtn.disabled = true;
                forwardBtn.disabled = true;
            }
        }
    }

    goBack() {
        const activeTab = this.tabs.find(t => t.id === this.activeTabId);
        if (activeTab && activeTab.webview && activeTab.webview.canGoBack()) {
            activeTab.webview.goBack();
        }
    }

    goForward() {
        const activeTab = this.tabs.find(t => t.id === this.activeTabId);
        if (activeTab && activeTab.webview && activeTab.webview.canGoForward()) {
            activeTab.webview.goForward();
        }
    }

    refresh() {
        const activeTab = this.tabs.find(t => t.id === this.activeTabId);
        if (activeTab && activeTab.webview) {
            activeTab.webview.reload();
        }
    }

    goHome() {
        this.navigateToUrl(this.settings.homepage);
    }

    // Developer Tools
    toggleDevTools() {
        const activeTab = this.tabs.find(t => t.id === this.activeTabId);
        if (activeTab && activeTab.webview) {
            if (activeTab.webview.isDevToolsOpened()) {
                activeTab.webview.closeDevTools();
            } else {
                activeTab.webview.openDevTools();
            }
        }
    }

    // Context Menus
    showTabContextMenu(event, tab) {
        this.contextMenuTarget = tab;
        const menu = document.getElementById('tabContextMenu');
        
        menu.style.left = event.pageX + 'px';
        menu.style.top = event.pageY + 'px';
        menu.classList.add('show');
    }

    hideContextMenu() {
        const contextMenu = document.getElementById('contextMenu');
        const tabContextMenu = document.getElementById('tabContextMenu');
        if (contextMenu) contextMenu.classList.remove('show');
        if (tabContextMenu) tabContextMenu.classList.remove('show');
    }

    // Utility methods
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return 'Just now';
        if (diff < 3600000) return `${Math.floor(diff / 60000)} minutes ago`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)} hours ago`;
        return date.toLocaleDateString();
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // Keep existing methods from original renderer.js
    setupEventListeners() {
        // Tab management
        document.getElementById('newTabBtn').addEventListener('click', () => {
            this.createNewTab();
        });

        // Navigation
        document.getElementById('backBtn').addEventListener('click', () => this.goBack());
        document.getElementById('forwardBtn').addEventListener('click', () => this.goForward());
        document.getElementById('refreshBtn').addEventListener('click', () => this.refresh());
        document.getElementById('homeBtn').addEventListener('click', () => this.goHome());

        // URL bar
        document.getElementById('urlInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.navigateToUrl(e.target.value);
            }
        });

        document.getElementById('goBtn').addEventListener('click', () => {
            const url = document.getElementById('urlInput').value;
            this.navigateToUrl(url);
        });

        // Find functionality
        document.getElementById('findBtn').addEventListener('click', () => this.showFindBar());
        document.getElementById('closeFindBtn').addEventListener('click', () => this.hideFindBar());
        
        document.getElementById('findInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.findInPage(e.target.value, !e.shiftKey);
            }
        });

        document.getElementById('findNextBtn').addEventListener('click', () => {
            const text = document.getElementById('findInput').value;
            if (text) this.findInPage(text, true);
        });

        document.getElementById('findPrevBtn').addEventListener('click', () => {
            const text = document.getElementById('findInput').value;
            if (text) this.findInPage(text, false);
        });

        // History and Downloads
        document.getElementById('historyBtn').addEventListener('click', () => this.showHistoryModal());
        document.getElementById('downloadsBtn').addEventListener('click', () => this.showDownloadsModal());
        document.getElementById('devToolsBtn').addEventListener('click', () => this.toggleDevTools());

        // Modal close buttons
        document.getElementById('closeHistoryModal').addEventListener('click', () => this.hideHistoryModal());
        document.getElementById('closeDownloadsModal').addEventListener('click', () => this.hideDownloadsModal());

        // History controls
        document.getElementById('clearHistoryBtn').addEventListener('click', () => this.clearHistory());
        document.getElementById('historySearch').addEventListener('input', (e) => {
            this.filterHistory(e.target.value);
        });

        // Download controls
        document.getElementById('clearDownloadsBtn').addEventListener('click', () => this.clearDownloads());
        document.getElementById('openDownloadsFolderBtn').addEventListener('click', () => {
            window.electronAPI.openDownloadsFolder();
        });

        // Tab context menu
        document.getElementById('closeTab').addEventListener('click', () => {
            if (this.contextMenuTarget) {
                this.closeTab(this.contextMenuTarget.id);
            }
        });

        document.getElementById('closeOtherTabs').addEventListener('click', () => {
            if (this.contextMenuTarget) {
                const keepTabId = this.contextMenuTarget.id;
                this.tabs.filter(t => t.id !== keepTabId).forEach(t => this.closeTab(t.id));
            }
        });

        document.getElementById('duplicateTab').addEventListener('click', () => {
            if (this.contextMenuTarget) {
                this.duplicateTab(this.contextMenuTarget.id);
            }
        });

        document.getElementById('reloadTab').addEventListener('click', () => {
            if (this.contextMenuTarget && this.contextMenuTarget.webview) {
                this.contextMenuTarget.webview.reload();
            }
        });

        // Keep all existing event listeners from original code
        this.setupOriginalEventListeners();
    }

    setupOriginalEventListeners() {
        // Add website button
        document.getElementById('addWebsiteBtn').addEventListener('click', () => {
            this.showAddWebsiteModal();
        });

        document.getElementById('welcomeAddBtn')?.addEventListener('click', () => {
            this.showAddWebsiteModal();
        });

        // Settings button
        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.showSettingsModal();
        });

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.filterWebsites(e.target.value);
        });

        // Zoom controls
        document.getElementById('zoomInBtn').addEventListener('click', () => {
            this.zoomIn();
        });

        document.getElementById('zoomOutBtn').addEventListener('click', () => {
            this.zoomOut();
        });

        // Fullscreen toggle
        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Add website modal
        document.getElementById('closeAddModal').addEventListener('click', () => {
            this.hideAddWebsiteModal();
        });

        document.getElementById('cancelAddBtn').addEventListener('click', () => {
            this.hideAddWebsiteModal();
        });

        document.getElementById('addWebsiteForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addWebsite();
        });

        // Settings modal
        document.getElementById('closeSettingsModal').addEventListener('click', () => {
            this.hideSettingsModal();
        });

        document.getElementById('saveSettingsBtn').addEventListener('click', () => {
            this.saveSettings();
        });

        document.getElementById('exportDataBtn').addEventListener('click', () => {
            this.exportWebsites();
        });

        document.getElementById('importDataBtn').addEventListener('click', () => {
            this.importWebsites();
        });

        document.getElementById('clearAllDataBtn').addEventListener('click', () => {
            this.clearAllData();
        });

        // Context menu
        document.addEventListener('click', () => {
            this.hideContextMenu();
        });

        document.getElementById('editWebsite').addEventListener('click', () => {
            this.editWebsite(this.contextMenuTarget);
        });

        document.getElementById('toggleFavorite').addEventListener('click', () => {
            this.toggleFavorite(this.contextMenuTarget);
        });

        document.getElementById('copyUrl').addEventListener('click', () => {
            this.copyUrl(this.contextMenuTarget);
        });

        document.getElementById('deleteWebsite').addEventListener('click', () => {
            this.deleteWebsite(this.contextMenuTarget);
        });

        // Modal overlay clicks
        document.getElementById('addWebsiteModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.hideAddWebsiteModal();
            }
        });

        document.getElementById('settingsModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.hideSettingsModal();
            }
        });

        document.getElementById('historyModal')?.addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.hideHistoryModal();
            }
        });

        document.getElementById('downloadsModal')?.addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.hideDownloadsModal();
            }
        });
    }

    setupElectronListeners() {
        window.electronAPI.onShowAddWebsiteDialog(() => {
            this.showAddWebsiteModal();
        });

        window.electronAPI.onReloadCurrentSite(() => {
            this.refresh();
        });

        window.electronAPI.onZoomIn(() => {
            this.zoomIn();
        });

        window.electronAPI.onZoomOut(() => {
            this.zoomOut();
        });

        window.electronAPI.onResetZoom(() => {
            this.resetZoom();
        });

        window.electronAPI.onClearAllData(() => {
            this.clearAllData();
        });

        window.electronAPI.onShowSettings(() => {
            this.showSettingsModal();
        });

        window.electronAPI.onExportWebsites((event, filePath) => {
            this.exportWebsitesToFile(filePath);
        });

        window.electronAPI.onImportWebsites((event, filePath) => {
            this.importWebsitesFromFile(filePath);
        });

        // New event listeners
        window.electronAPI.onShowHistory(() => {
            this.showHistoryModal();
        });

        window.electronAPI.onShowDownloads(() => {
            this.showDownloadsModal();
        });

        window.electronAPI.onShowFind(() => {
            this.showFindBar();
        });

        window.electronAPI.onToggleDevTools(() => {
            this.toggleDevTools();
        });

        window.electronAPI.onNewTab(() => {
            this.createNewTab();
        });

        window.electronAPI.onCloseTab(() => {
            if (this.activeTabId) {
                this.closeTab(this.activeTabId);
            }
        });

        window.electronAPI.onNextTab(() => {
            const currentIndex = this.tabs.findIndex(t => t.id === this.activeTabId);
            const nextIndex = (currentIndex + 1) % this.tabs.length;
            this.switchToTab(this.tabs[nextIndex].id);
        });

        window.electronAPI.onPrevTab(() => {
            const currentIndex = this.tabs.findIndex(t => t.id === this.activeTabId);
            const prevIndex = currentIndex === 0 ? this.tabs.length - 1 : currentIndex - 1;
            this.switchToTab(this.tabs[prevIndex].id);
        });

        // Download event listeners
        window.electronAPI.onDownloadStarted((event, download) => {
            this.handleDownloadStarted(download);
        });

        window.electronAPI.onDownloadProgress((event, progress) => {
            this.handleDownloadProgress(progress);
        });

        window.electronAPI.onDownloadCompleted((event, download) => {
            this.handleDownloadCompleted(download);
        });

        window.electronAPI.onDownloadFailed((event, download) => {
            this.handleDownloadFailed(download);
        });
    }

    // Download event handlers
    handleDownloadStarted(downloadInfo) {
        const download = {
            id: this.generateId(),
            filename: downloadInfo.filename,
            url: downloadInfo.url,
            state: 'in_progress',
            progress: 0,
            totalBytes: downloadInfo.totalBytes || 0,
            receivedBytes: 0,
            startTime: new Date().toISOString()
        };

        this.downloads.unshift(download);
        this.saveData();
        
        if (this.settings.showNotifications) {
            this.showNotification(`Download started: ${download.filename}`);
        }
    }

    handleDownloadProgress(progressInfo) {
        const download = this.downloads.find(d => d.filename === progressInfo.filename);
        if (download) {
            download.progress = progressInfo.progress;
            download.receivedBytes = progressInfo.receivedBytes;
            this.renderDownloads();
        }
    }

    handleDownloadCompleted(downloadInfo) {
        const download = this.downloads.find(d => d.filename === downloadInfo.filename);
        if (download) {
            download.state = 'completed';
            download.progress = 100;
            download.path = downloadInfo.path;
            this.saveData();
            this.renderDownloads();
            
            if (this.settings.showNotifications) {
                this.showNotification(`Download completed: ${download.filename}`);
            }
        }
    }

    handleDownloadFailed(downloadInfo) {
        const download = this.downloads.find(d => d.filename === downloadInfo.filename);
        if (download) {
            download.state = 'failed';
            download.error = downloadInfo.error;
            this.saveData();
            this.renderDownloads();
            
            if (this.settings.showNotifications) {
                this.showNotification(`Download failed: ${download.filename}`);
            }
        }
    }

    // Keep all existing methods from original renderer.js
    showWelcomeScreen() {
        const welcomeScreen = document.getElementById('welcomeScreen');
        if (welcomeScreen) {
            welcomeScreen.style.display = 'flex';
        }
    }

    hideWelcomeScreen() {
        const welcomeScreen = document.getElementById('welcomeScreen');
        if (welcomeScreen) {
            welcomeScreen.style.display = 'none';
        }
    }

    showAddWebsiteModal() {
        const modal = document.getElementById('addWebsiteModal');
        const nameInput = document.getElementById('websiteName');
        if (modal) {
            modal.classList.add('show');
        }
        if (nameInput) {
            nameInput.focus();
        }
    }

    hideAddWebsiteModal() {
        const modal = document.getElementById('addWebsiteModal');
        const form = document.getElementById('addWebsiteForm');
        if (modal) {
            modal.classList.remove('show');
        }
        if (form) {
            form.reset();
        }
    }

    showSettingsModal() {
        const autoLoadCheckbox = document.getElementById('autoLoadLastWebsite');
        const notificationsCheckbox = document.getElementById('showNotifications');
        const clearDataCheckbox = document.getElementById('clearDataOnExit');
        const blockAdsCheckbox = document.getElementById('blockAds');
        const modal = document.getElementById('settingsModal');
        
        if (autoLoadCheckbox) {
            autoLoadCheckbox.checked = this.settings.autoLoadLastWebsite;
        }
        if (notificationsCheckbox) {
            notificationsCheckbox.checked = this.settings.showNotifications;
        }
        if (clearDataCheckbox) {
            clearDataCheckbox.checked = this.settings.clearDataOnExit;
        }
        if (blockAdsCheckbox) {
            blockAdsCheckbox.checked = this.settings.blockAds;
        }
        if (modal) {
            modal.classList.add('show');
        }
    }

    hideSettingsModal() {
        const modal = document.getElementById('settingsModal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    async addWebsite() {
        const name = document.getElementById('websiteName').value.trim();
        const url = document.getElementById('websiteUrl').value.trim();
        const category = document.getElementById('websiteCategory').value;
        const favorite = document.getElementById('websiteFavorite').checked;

        if (!name || !url) {
            await window.electronAPI.showMessageBox({
                type: 'error',
                title: 'Error',
                message: 'Please fill in all required fields.'
            });
            return;
        }

        try {
            new URL(url);
        } catch {
            await window.electronAPI.showMessageBox({
                type: 'error',
                title: 'Invalid URL',
                message: 'Please enter a valid URL.'
            });
            return;
        }

        const website = {
            id: this.generateId(),
            name,
            url,
            category,
            favorite,
            addedAt: new Date().toISOString(),
            lastVisited: null
        };

        this.websites.push(website);
        await this.saveData();
        this.renderWebsiteList();
        this.updateStats();
        this.hideAddWebsiteModal();
        this.hideWelcomeScreen();

        this.loadWebsite(website);
    }

    loadWebsite(website) {
        this.currentWebsite = website;
        website.lastVisited = new Date().toISOString();
        this.saveData();

        document.querySelectorAll('.website-item').forEach(item => {
            item.classList.remove('active');
        });
        const activeElement = document.querySelector(`[data-website-id="${website.id}"]`);
        if (activeElement) {
            activeElement.classList.add('active');
        }

        this.navigateToUrl(website.url);
        this.hideWelcomeScreen();

        window.electronAPI.storeSet('lastWebsite', website.id);
    }

    renderWebsiteList() {
        const container = document.getElementById('websiteList');
        container.innerHTML = '';

        const sortedWebsites = [...this.websites].sort((a, b) => {
            if (a.favorite && !b.favorite) return -1;
            if (!a.favorite && b.favorite) return 1;
            
            const aTime = new Date(a.lastVisited || a.addedAt).getTime();
            const bTime = new Date(b.lastVisited || b.addedAt).getTime();
            return bTime - aTime;
        });

        sortedWebsites.forEach(website => {
            const item = this.createWebsiteItem(website);
            container.appendChild(item);
        });
    }

    createWebsiteItem(website) {
        const item = document.createElement('div');
        item.className = 'website-item';
        if (website.favorite) item.classList.add('favorite');
        item.setAttribute('data-website-id', website.id);

        const favicon = document.createElement('div');
        favicon.className = 'website-favicon';
        favicon.textContent = website.name.charAt(0).toUpperCase();

        const info = document.createElement('div');
        info.className = 'website-info';

        const name = document.createElement('div');
        name.className = 'website-name';
        name.textContent = website.name;

        const url = document.createElement('div');
        url.className = 'website-url';
        url.textContent = website.url;

        const category = document.createElement('div');
        category.className = 'website-category';
        category.textContent = website.category;

        info.appendChild(name);
        info.appendChild(url);
        info.appendChild(category);

        item.appendChild(favicon);
        item.appendChild(info);

        item.addEventListener('click', () => {
            this.loadWebsite(website);
        });

        item.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showWebsiteContextMenu(e, website);
        });

        return item;
    }

    showWebsiteContextMenu(event, website) {
        this.contextMenuTarget = website;
        const menu = document.getElementById('contextMenu');
        
        menu.style.left = event.pageX + 'px';
        menu.style.top = event.pageY + 'px';
        menu.classList.add('show');

        const favoriteItem = document.getElementById('toggleFavorite');
        favoriteItem.innerHTML = website.favorite 
            ? '<i class="fas fa-star"></i> Remove from Favorites'
            : '<i class="far fa-star"></i> Add to Favorites';
    }

    filterWebsites(query) {
        const items = document.querySelectorAll('.website-item');
        const lowerQuery = query.toLowerCase();

        items.forEach(item => {
            const name = item.querySelector('.website-name').textContent.toLowerCase();
            const url = item.querySelector('.website-url').textContent.toLowerCase();
            const category = item.querySelector('.website-category').textContent.toLowerCase();

            const matches = name.includes(lowerQuery) || 
                          url.includes(lowerQuery) || 
                          category.includes(lowerQuery);

            item.style.display = matches ? 'flex' : 'none';
        });
    }

    filterHistory(query) {
        const items = document.querySelectorAll('.history-item');
        const lowerQuery = query.toLowerCase();

        items.forEach(item => {
            const title = item.querySelector('.history-title').textContent.toLowerCase();
            const url = item.querySelector('.history-url').textContent.toLowerCase();

            const matches = title.includes(lowerQuery) || url.includes(lowerQuery);
            item.style.display = matches ? 'flex' : 'none';
        });
    }

    updateStats() {
        const count = this.websites.length;
        const text = count === 1 ? '1 website' : `${count} websites`;
        const websiteCountElement = document.getElementById('websiteCount');
        if (websiteCountElement) {
            websiteCountElement.textContent = text;
        }
    }

    async deleteWebsite(website) {
        const result = await window.electronAPI.showMessageBox({
            type: 'question',
            title: 'Delete Website',
            message: `Are you sure you want to delete "${website.name}"?`,
            buttons: ['Delete', 'Cancel'],
            defaultId: 1
        });

        if (result.response === 0) {
            this.websites = this.websites.filter(w => w.id !== website.id);
            await this.saveData();
            this.renderWebsiteList();
            this.updateStats();

            if (this.currentWebsite && this.currentWebsite.id === website.id) {
                this.currentWebsite = null;
                if (this.websites.length === 0) {
                    this.showWelcomeScreen();
                }
            }
        }
    }

    async toggleFavorite(website) {
        website.favorite = !website.favorite;
        await this.saveData();
        this.renderWebsiteList();
    }

    async copyUrl(website) {
        try {
            await navigator.clipboard.writeText(website.url);
            if (this.settings.showNotifications) {
                this.showNotification('URL copied to clipboard');
            }
        } catch (error) {
            console.error('Failed to copy URL:', error);
        }
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #0078d4;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 10000;
            animation: slideIn 0.3s ease-out;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    showLoadingIndicator() {
        const loadingIndicator = document.getElementById('loadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'flex';
        }
    }

    hideLoadingIndicator() {
        const loadingIndicator = document.getElementById('loadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
    }

    zoomIn() {
        this.zoomLevel = Math.min(this.zoomLevel + 0.1, 3.0);
        this.applyZoom();
    }

    zoomOut() {
        this.zoomLevel = Math.max(this.zoomLevel - 0.1, 0.3);
        this.applyZoom();
    }

    resetZoom() {
        this.zoomLevel = 1.0;
        this.applyZoom();
    }

    applyZoom() {
        const activeTab = this.tabs.find(t => t.id === this.activeTabId);
        if (activeTab && activeTab.webview && typeof activeTab.webview.setZoomFactor === 'function') {
            activeTab.webview.setZoomFactor(this.zoomLevel);
        }
        this.updateZoomDisplay();
    }

    updateZoomDisplay() {
        const zoomLevelElement = document.getElementById('zoomLevel');
        if (zoomLevelElement) {
            zoomLevelElement.textContent = Math.round(this.zoomLevel * 100) + '%';
        }
    }

    toggleFullscreen() {
        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            document.documentElement.requestFullscreen();
        }
    }

    async saveSettings() {
        this.settings.autoLoadLastWebsite = document.getElementById('autoLoadLastWebsite').checked;
        this.settings.showNotifications = document.getElementById('showNotifications').checked;
        this.settings.clearDataOnExit = document.getElementById('clearDataOnExit').checked;
        this.settings.blockAds = document.getElementById('blockAds').checked;

        await this.saveData();
        this.hideSettingsModal();
        
        if (this.settings.showNotifications) {
            this.showNotification('Settings saved successfully');
        }
    }

    async exportWebsites() {
        try {
            const result = await window.electronAPI.showSaveDialog({
                filters: [
                    { name: 'JSON Files', extensions: ['json'] }
                ],
                defaultPath: 'websites-backup.json'
            });

            if (!result.canceled) {
                await this.exportWebsitesToFile(result.filePath);
            }
        } catch (error) {
            console.error('Export error:', error);
        }
    }

    async exportWebsitesToFile(filePath) {
        try {
            const data = {
                websites: this.websites,
                settings: this.settings,
                history: this.history,
                exportDate: new Date().toISOString(),
                version: await window.electronAPI.getAppVersion()
            };

            await window.electronAPI.writeFile(filePath, JSON.stringify(data, null, 2));
            
            if (this.settings.showNotifications) {
                this.showNotification('Data exported successfully');
            }
        } catch (error) {
            await window.electronAPI.showMessageBox({
                type: 'error',
                title: 'Export Error',
                message: 'Failed to export data: ' + error.message
            });
        }
    }

    async importWebsites() {
        try {
            const result = await window.electronAPI.showOpenDialog({
                filters: [
                    { name: 'JSON Files', extensions: ['json'] }
                ],
                properties: ['openFile']
            });

            if (!result.canceled && result.filePaths.length > 0) {
                await this.importWebsitesFromFile(result.filePaths[0]);
            }
        } catch (error) {
            console.error('Import error:', error);
        }
    }

    async importWebsitesFromFile(filePath) {
        try {
            const fileContent = await window.electronAPI.readFile(filePath);
            const data = JSON.parse(fileContent);

            if (data.websites && Array.isArray(data.websites)) {
                const result = await window.electronAPI.showMessageBox({
                    type: 'question',
                    title: 'Import Data',
                    message: `Import ${data.websites.length} websites and other data? This will add to your existing collection.`,
                    buttons: ['Import', 'Cancel'],
                    defaultId: 0
                });

                if (result.response === 0) {
                    data.websites.forEach(website => {
                        website.id = this.generateId();
                        website.addedAt = new Date().toISOString();
                    });

                    this.websites.push(...data.websites);
                    
                    if (data.history) {
                        this.history.push(...data.history);
                    }

                    await this.saveData();
                    this.renderWebsiteList();
                    this.updateStats();

                    if (this.settings.showNotifications) {
                        this.showNotification(`Imported ${data.websites.length} websites`);
                    }
                }
            } else {
                throw new Error('Invalid file format');
            }
        } catch (error) {
            await window.electronAPI.showMessageBox({
                type: 'error',
                title: 'Import Error',
                message: 'Failed to import data: ' + error.message
            });
        }
    }

    async clearAllData() {
        const result = await window.electronAPI.showMessageBox({
            type: 'warning',
            title: 'Clear All Data',
            message: 'This will permanently delete all websites, history, downloads, and settings. Are you sure?',
            buttons: ['Clear All Data', 'Cancel'],
            defaultId: 1
        });

        if (result.response === 0) {
            this.websites = [];
            this.history = [];
            this.downloads = [];
            this.settings = {
                autoLoadLastWebsite: false,
                showNotifications: true,
                clearDataOnExit: false,
                blockAds: false,
                homepage: 'about:blank'
            };

            // Close all tabs except one
            while (this.tabs.length > 1) {
                this.closeTab(this.tabs[0].id);
            }

            await window.electronAPI.storeClear();
            this.renderWebsiteList();
            this.updateStats();
            this.showWelcomeScreen();
            this.hideSettingsModal();
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing UnrestrictedBrowser...');
    try {
        const browser = new UnrestrictedBrowser();
        console.log('UnrestrictedBrowser initialized successfully');
    } catch (error) {
        console.error('Error initializing UnrestrictedBrowser:', error);
    }
});