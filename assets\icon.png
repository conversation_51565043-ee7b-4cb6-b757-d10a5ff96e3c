<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0078d4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#106ebe;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="128" cy="128" r="120" fill="url(#grad1)" stroke="#ffffff" stroke-width="4"/>
  
  <!-- Globe icon -->
  <circle cx="128" cy="128" r="80" fill="none" stroke="#ffffff" stroke-width="6"/>
  <path d="M 48 128 Q 128 80 208 128" fill="none" stroke="#ffffff" stroke-width="4"/>
  <path d="M 48 128 Q 128 176 208 128" fill="none" stroke="#ffffff" stroke-width="4"/>
  <line x1="128" y1="48" x2="128" y2="208" stroke="#ffffff" stroke-width="4"/>
  <line x1="48" y1="128" x2="208" y2="128" stroke="#ffffff" stroke-width="4"/>
  
  <!-- Browser window overlay -->
  <rect x="80" y="90" width="96" height="76" rx="8" fill="rgba(255,255,255,0.2)" stroke="#ffffff" stroke-width="2"/>
  <rect x="80" y="90" width="96" height="16" rx="8" fill="rgba(255,255,255,0.4)"/>
  <circle cx="90" cy="98" r="3" fill="#ffffff"/>
  <circle cx="102" cy="98" r="3" fill="#ffffff"/>
  <circle cx="114" cy="98" r="3" fill="#ffffff"/>
</svg>