/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #1a1a1a;
    color: #e0e0e0;
    overflow: hidden;
    user-select: none;
}

/* App Container */
.app-container {
    display: flex;
    height: 100vh;
    width: 100vw;
    background: #1a1a1a;
    overflow: hidden;
}

/* Side Panel */
.side-panel {
    width: 300px;
    background: #2d2d2d;
    border-right: 1px solid #404040;
    display: flex;
    flex-direction: column;
    min-width: 250px;
    max-width: 400px;
    flex-shrink: 0;
    overflow: hidden;
}

.panel-header {
    padding: 16px;
    border-bottom: 1px solid #404040;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #333333;
}

.panel-header h2 {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 8px;
}

.panel-controls {
    display: flex;
    gap: 8px;
}

.search-container {
    padding: 12px 16px;
    position: relative;
    border-bottom: 1px solid #404040;
}

.search-input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    background: #1a1a1a;
    border: 1px solid #404040;
    border-radius: 6px;
    color: #e0e0e0;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.search-input:focus {
    border-color: #0078d4;
}

.search-icon {
    position: absolute;
    left: 28px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
    font-size: 14px;
}

.website-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.website-item {
    padding: 12px 16px;
    cursor: pointer;
    border-left: 3px solid transparent;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
}

.website-item:hover {
    background: #3a3a3a;
    border-left-color: #0078d4;
}

.website-item.active {
    background: #0078d4;
    border-left-color: #ffffff;
    color: #ffffff;
}

.website-item.favorite::before {
    content: '\f005';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 16px;
    color: #ffd700;
    font-size: 12px;
}

.website-favicon {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    background: #404040;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #888;
    flex-shrink: 0;
}

.website-info {
    flex: 1;
    min-width: 0;
}

.website-name {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.website-url {
    font-size: 12px;
    color: #888;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.website-category {
    font-size: 10px;
    background: #404040;
    color: #ccc;
    padding: 2px 6px;
    border-radius: 10px;
    margin-top: 4px;
    display: inline-block;
}

.panel-footer {
    padding: 12px 16px;
    border-top: 1px solid #404040;
    background: #333333;
}

.stats {
    font-size: 12px;
    color: #888;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #1a1a1a;
    min-height: 0;
    min-width: 0;
    width: 100%;
    overflow: hidden;
}

.top-bar {
    height: 50px;
    background: #2d2d2d;
    border-bottom: 1px solid #404040;
    display: flex;
    align-items: center;
    padding: 0 16px;
    gap: 16px;
}

.navigation-controls {
    display: flex;
    gap: 8px;
}

.url-bar {
    flex: 1;
    display: flex;
    gap: 8px;
    max-width: 600px;
}

.url-input {
    flex: 1;
    padding: 8px 12px;
    background: #1a1a1a;
    border: 1px solid #404040;
    border-radius: 6px;
    color: #e0e0e0;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.url-input:focus {
    border-color: #0078d4;
}

.view-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.zoom-level {
    font-size: 12px;
    color: #888;
    min-width: 40px;
    text-align: center;
}

/* Buttons */
.btn-icon {
    width: 32px;
    height: 32px;
    background: transparent;
    border: 1px solid #404040;
    border-radius: 6px;
    color: #e0e0e0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    font-size: 14px;
}

.btn-icon:hover {
    background: #404040;
    border-color: #555;
}

.btn-icon:active {
    background: #555;
}

.btn-icon:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-icon:disabled:hover {
    background: transparent;
    border-color: #404040;
}

.btn-primary {
    background: #0078d4;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary:hover {
    background: #106ebe;
}

.btn-secondary {
    background: #404040;
    color: #e0e0e0;
    border: 1px solid #555;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-secondary:hover {
    background: #555;
    border-color: #666;
}

.btn-danger {
    background: #d13438;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-danger:hover {
    background: #b02a2e;
}

/* Webview Container */
.webview-container {
    flex: 1;
    position: relative;
    background: #1a1a1a;
    overflow: hidden;
    min-height: 0;
    min-width: 0;
    width: 100%;
}

.webview-container webview {
    width: 100% !important;
    height: 100% !important;
    background: #ffffff !important;
    display: block !important;
    border: none !important;
    outline: none !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    max-width: none !important;
    min-width: 0 !important;
    box-sizing: border-box !important;
}

/* Welcome Screen */
.welcome-screen {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1a1a1a;
}

.welcome-content {
    text-align: center;
    max-width: 400px;
    padding: 40px;
}

.welcome-icon {
    font-size: 64px;
    color: #0078d4;
    margin-bottom: 24px;
}

.welcome-content h1 {
    font-size: 28px;
    margin-bottom: 16px;
    color: #ffffff;
}

.welcome-content p {
    font-size: 16px;
    color: #888;
    margin-bottom: 32px;
    line-height: 1.5;
}

/* Loading Indicator */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    color: #888;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #404040;
    border-top: 3px solid #0078d4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modals */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-overlay.show {
    display: flex;
}

.modal {
    background: #2d2d2d;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #404040;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #333333;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.modal-close {
    background: transparent;
    border: none;
    color: #888;
    cursor: pointer;
    padding: 4px;
}

.modal-close:hover {
    color: #ffffff;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #404040;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background: #333333;
}

/* Form Elements */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #e0e0e0;
}

.form-group input[type="text"],
.form-group input[type="url"],
.form-group select {
    width: 100%;
    padding: 10px 12px;
    background: #1a1a1a;
    border: 1px solid #404040;
    border-radius: 6px;
    color: #e0e0e0;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.form-group input[type="text"]:focus,
.form-group input[type="url"]:focus,
.form-group select:focus {
    border-color: #0078d4;
}

.form-group input[type="checkbox"] {
    margin-right: 8px;
}

.form-group label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    cursor: pointer;
}

/* Settings */
.settings-section {
    margin-bottom: 24px;
}

.settings-section h4 {
    font-size: 16px;
    margin-bottom: 12px;
    color: #ffffff;
    border-bottom: 1px solid #404040;
    padding-bottom: 8px;
}

/* Context Menu */
.context-menu {
    position: fixed;
    background: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 6px;
    padding: 4px 0;
    min-width: 150px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    display: none;
}

.context-menu.show {
    display: block;
}

.context-item {
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background 0.2s;
}

.context-item:hover {
    background: #404040;
}

.context-item.context-danger {
    color: #ff6b6b;
}

.context-item.context-danger:hover {
    background: #d13438;
    color: white;
}

.context-separator {
    height: 1px;
    background: #404040;
    margin: 4px 0;
    cursor: default;
}

.context-separator:hover {
    background: #404040;
}

/* Scrollbars */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #666;
}

/* Responsive */
@media (max-width: 1200px) {
    .side-panel {
        width: 250px;
    }
}

@media (max-width: 900px) {
    .url-bar {
        max-width: 400px;
    }
    
    .view-controls .zoom-level {
        display: none;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.website-item {
    animation: slideIn 0.2s ease-out;
}

.modal {
    animation: slideIn 0.3s ease-out;
}

/* Tab Bar */
.tab-bar {
    height: 40px;
    background: #2d2d2d;
    border-bottom: 1px solid #404040;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.tabs-container {
    flex: 1;
    display: flex;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.tabs-container::-webkit-scrollbar {
    display: none;
}

.tab {
    min-width: 200px;
    max-width: 250px;
    height: 32px;
    background: #404040;
    border: 1px solid #555;
    border-bottom: none;
    border-radius: 8px 8px 0 0;
    margin-right: 2px;
    display: flex;
    align-items: center;
    padding: 0 12px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.tab:hover {
    background: #4a4a4a;
}

.tab.active {
    background: #1a1a1a;
    border-color: #0078d4;
    color: #ffffff;
}

.tab-favicon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    flex-shrink: 0;
}

.tab-title {
    flex: 1;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tab-close {
    width: 16px;
    height: 16px;
    margin-left: 8px;
    border-radius: 50%;
    background: transparent;
    border: none;
    color: #888;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    transition: all 0.2s;
}

.tab-close:hover {
    background: #666;
    color: #fff;
}

.new-tab-btn {
    margin: 0 8px;
    width: 28px;
    height: 28px;
}

/* Find Bar */
.find-bar {
    height: 40px;
    background: #333333;
    border-bottom: 1px solid #404040;
    display: none;
    align-items: center;
    padding: 0 16px;
    gap: 8px;
}

.find-bar.show {
    display: flex;
}

.find-input {
    width: 200px;
    padding: 6px 10px;
    background: #1a1a1a;
    border: 1px solid #404040;
    border-radius: 4px;
    color: #e0e0e0;
    font-size: 13px;
    outline: none;
}

.find-input:focus {
    border-color: #0078d4;
}

.find-results {
    font-size: 12px;
    color: #888;
    min-width: 40px;
}

/* Security Indicator */
.security-indicator {
    display: flex;
    align-items: center;
    padding: 0 8px;
    color: #888;
    font-size: 12px;
}

.security-indicator.secure {
    color: #4caf50;
}

.security-indicator.insecure {
    color: #f44336;
}

/* Large Modal */
.large-modal {
    width: 80%;
    max-width: 900px;
    height: 70%;
    max-height: 600px;
}

.large-modal .modal-body {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* History Styles */
.history-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    align-items: center;
}

.history-controls .search-input {
    flex: 1;
    max-width: 300px;
}

.history-list {
    flex: 1;
    overflow-y: auto;
}

.history-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #404040;
    cursor: pointer;
    transition: background 0.2s;
}

.history-item:hover {
    background: #3a3a3a;
}

.history-favicon {
    width: 16px;
    height: 16px;
    margin-right: 12px;
    flex-shrink: 0;
}

.history-info {
    flex: 1;
    min-width: 0;
}

.history-title {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.history-url {
    font-size: 12px;
    color: #888;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.history-time {
    font-size: 11px;
    color: #666;
    margin-left: 12px;
    flex-shrink: 0;
}

/* Downloads Styles */
.downloads-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.downloads-list {
    flex: 1;
    overflow-y: auto;
}

.download-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #404040;
    transition: background 0.2s;
}

.download-item:hover {
    background: #3a3a3a;
}

.download-icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #404040;
    border-radius: 4px;
    color: #888;
    flex-shrink: 0;
}

.download-info {
    flex: 1;
    min-width: 0;
}

.download-name {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.download-details {
    font-size: 12px;
    color: #888;
    display: flex;
    gap: 12px;
}

.download-progress {
    width: 100%;
    height: 4px;
    background: #404040;
    border-radius: 2px;
    margin-top: 4px;
    overflow: hidden;
}

.download-progress-bar {
    height: 100%;
    background: #0078d4;
    transition: width 0.3s;
}

.download-actions {
    display: flex;
    gap: 8px;
    margin-left: 12px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #666;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
}

/* Tab Context Menu */
#tabContextMenu {
    min-width: 180px;
}

/* Loading States */
.tab.loading .tab-favicon::after {
    content: '';
    width: 12px;
    height: 12px;
    border: 2px solid #404040;
    border-top: 2px solid #0078d4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.loading {
    pointer-events: none;
    opacity: 0.7;
}