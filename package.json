{"name": "unrestricted-browser", "version": "1.0.0", "description": "A desktop application for unrestricted website browsing", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux"}, "keywords": ["electron", "browser", "desktop", "unrestricted"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^25.9.0", "electron-builder": "^24.6.4"}, "dependencies": {"electron-store": "^8.1.0"}, "build": {"appId": "com.yourcompany.unrestricted-browser", "productName": "Unrestricted Browser", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}